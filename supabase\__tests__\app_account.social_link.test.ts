import { describe, test, expect, afterAll, beforeAll } from "vitest";
import { mockAdmin, mockCustomer, mockProvider } from "./mocks/auth.user";
import { createSetupHooks } from "./utils/createSetupHooks";
import { serviceClient } from "./utils/client";

createSetupHooks();

const customer1 = mockCustomer();
const customer2 = mockCustomer();
const provider = mockProvider();
const admin = mockAdmin();
let platformId: number;
const createdSocialLinkIds: string[] = [];
const createdPlatformIds: number[] = [];

beforeAll(async () => {
  // Create a test platform for social links
  const { data: platform } = await serviceClient
    .schema("app_catalog")
    .from("platform")
    .insert({
      name: "Test Platform"
    })
    .select()
    .single();

  if (!platform?.id) throw new Error("Failed to create test platform");
  platformId = platform.id;
  createdPlatformIds.push(platformId);
});

afterAll(async () => {
  // Clean up created social link records
  if (createdSocialLinkIds.length > 0) {
    await serviceClient
      .schema("app_account")
      .from("social_link")
      .delete()
      .in("id", createdSocialLinkIds);
  }

  // Clean up created platforms
  if (createdPlatformIds.length > 0) {
    await serviceClient
      .schema("app_catalog")
      .from("platform")
      .delete()
      .in("id", createdPlatformIds);
  }
});

describe("Basic CRUD Operations", () => {
  test("user can create their own social link", async () => {
    if (!customer1.client || !customer1.data)
      throw new Error("Customer not defined");

    const { data, error } = await customer1.client
      .schema("app_account")
      .from("social_link")
      .insert({
        user_id: customer1.data.id,
        platform: platformId,
        link: "https://example.com/user1"
      })
      .select()
      .single();

    expect(error).toBeNull();
    expect(data?.user_id).toBe(customer1.data.id);
    expect(data?.platform).toBe(platformId);
    expect(data?.link).toBe("https://example.com/user1");
    expect(data?.id).toBeDefined();
    expect(data?.created_at).toBeDefined();

    // Track created ID for cleanup
    if (data?.id) {
      createdSocialLinkIds.push(data.id);
    }
  });

  test("user can update their own social link", async () => {
    if (!customer1.client || !customer1.data)
      throw new Error("Customer not defined");

    // First get the social link
    const { data: socialLinks } = await customer1.client
      .schema("app_account")
      .from("social_link")
      .select("*")
      .eq("user_id", customer1.data.id)
      .limit(1);

    if (!socialLinks?.[0]?.id) throw new Error("No social link found");

    const { data, error } = await customer1.client
      .schema("app_account")
      .from("social_link")
      .update({
        link: "https://example.com/updated-user1"
      })
      .eq("id", socialLinks[0].id)
      .select()
      .single();

    expect(error).toBeNull();
    expect(data?.link).toBe("https://example.com/updated-user1");
    expect(data?.user_id).toBe(customer1.data.id);
    expect(data?.platform).toBe(platformId);
  });

  test("user can delete their own social link", async () => {
    if (!customer1.client || !customer1.data)
      throw new Error("Customer not defined");

    // Create a social link to delete
    const { data: newLink } = await customer1.client
      .schema("app_account")
      .from("social_link")
      .insert({
        user_id: customer1.data.id,
        platform: platformId,
        link: "https://example.com/to-delete"
      })
      .select()
      .single();

    if (!newLink?.id) throw new Error("Failed to create social link");

    const { error } = await customer1.client
      .schema("app_account")
      .from("social_link")
      .delete()
      .eq("id", newLink.id);

    expect(error).toBeNull();

    // Verify it's deleted
    const { data: deletedLink } = await customer1.client
      .schema("app_account")
      .from("social_link")
      .select("*")
      .eq("id", newLink.id);

    expect(deletedLink).toHaveLength(0);

    // No need to track this ID since it's deleted
  });
});

describe("RLS Policy Tests", () => {
  test("user cannot create social links for other users", async () => {
    if (!customer1.client || !customer2.data)
      throw new Error("Customers not defined");

    const { data, error } = await customer1.client
      .schema("app_account")
      .from("social_link")
      .insert({
        user_id: customer2.data.id,
        platform: platformId,
        link: "https://example.com/unauthorized"
      })
      .select();

    // Should either fail with error or return empty data due to RLS
    if (error === null) {
      expect(data).toHaveLength(0);
    } else {
      expect(error).not.toBeNull();
    }
  });

  test("user cannot update other users' social links", async () => {
    if (!customer1.client || !customer2.client || !customer2.data)
      throw new Error("Customers not defined");

    // Customer2 creates a social link
    const { data: customer2Link } = await customer2.client
      .schema("app_account")
      .from("social_link")
      .insert({
        user_id: customer2.data.id,
        platform: platformId,
        link: "https://example.com/customer2"
      })
      .select()
      .single();

    if (!customer2Link?.id) throw new Error("Failed to create customer2 link");

    // Track created ID for cleanup
    createdSocialLinkIds.push(customer2Link.id);

    // Customer1 tries to update customer2's link
    const { data, error } = await customer1.client
      .schema("app_account")
      .from("social_link")
      .update({
        link: "https://example.com/hacked"
      })
      .eq("id", customer2Link.id)
      .select();

    // RLS may return success but with 0 affected rows
    if (error === null) {
      expect(data).toHaveLength(0);
    } else {
      expect(error).not.toBeNull();
    }

    // Verify the link wasn't actually updated
    const { data: unchangedLink } = await customer2.client
      .schema("app_account")
      .from("social_link")
      .select("*")
      .eq("id", customer2Link.id)
      .single();

    expect(unchangedLink?.link).toBe("https://example.com/customer2");
  });

  test("user cannot delete other users' social links", async () => {
    if (!customer1.client || !customer2.client || !customer2.data)
      throw new Error("Customers not defined");

    // Get customer2's social link
    const { data: customer2Links } = await customer2.client
      .schema("app_account")
      .from("social_link")
      .select("*")
      .eq("user_id", customer2.data.id)
      .limit(1);

    if (!customer2Links?.[0]?.id) throw new Error("No customer2 link found");
    const linkId = customer2Links[0].id;

    // Customer1 tries to delete customer2's link
    const { data, error } = await customer1.client
      .schema("app_account")
      .from("social_link")
      .delete()
      .eq("id", linkId)
      .select();

    // RLS may return success but with 0 affected rows
    if (error === null) {
      expect(data).toHaveLength(0);
    } else {
      expect(error).not.toBeNull();
    }

    // Verify the link still exists
    const { data: stillExists } = await customer2.client
      .schema("app_account")
      .from("social_link")
      .select("*")
      .eq("id", linkId);

    expect(stillExists).toHaveLength(1);
  });

  test("all users can view all social links (public)", async () => {
    if (!customer1.client || !customer2.client || !customer2.data)
      throw new Error("Customers not defined");

    // Customer1 can view customer2's social links
    const { data, error } = await customer1.client
      .schema("app_account")
      .from("social_link")
      .select("*")
      .eq("user_id", customer2.data.id);

    expect(error).toBeNull();
    expect(data).toBeDefined();
  });
});

describe("Admin Access Tests", () => {
  test("admin can create social links for any user", async () => {
    if (!admin.client || !customer1.data)
      throw new Error("Admin or customer not defined");

    const { data, error } = await admin.client
      .schema("app_account")
      .from("social_link")
      .insert({
        user_id: customer1.data.id,
        platform: platformId,
        link: "https://example.com/admin-created"
      })
      .select()
      .single();

    expect(error).toBeNull();
    expect(data?.user_id).toBe(customer1.data.id);
    expect(data?.link).toBe("https://example.com/admin-created");
    expect(data?.platform).toBe(platformId);
    expect(data?.id).toBeDefined();

    // Track created ID for cleanup
    if (data?.id) {
      createdSocialLinkIds.push(data.id);
    }
  });

  test("admin can update any user's social links", async () => {
    if (!admin.client || !customer1.data)
      throw new Error("Admin or customer not defined");

    // Get a social link
    const { data: socialLinks } = await admin.client
      .schema("app_account")
      .from("social_link")
      .select("*")
      .eq("user_id", customer1.data.id)
      .limit(1);

    if (!socialLinks?.[0]?.id) throw new Error("No social link found");

    const { data, error } = await admin.client
      .schema("app_account")
      .from("social_link")
      .update({
        link: "https://example.com/admin-updated"
      })
      .eq("id", socialLinks[0].id)
      .select()
      .single();

    expect(error).toBeNull();
    expect(data?.link).toBe("https://example.com/admin-updated");
    expect(data?.user_id).toBe(customer1.data.id);
    expect(data?.platform).toBe(platformId);
    expect(data?.id).toBe(socialLinks[0].id);
  });

  test("admin can delete any user's social links", async () => {
    if (!admin.client || !customer1.data)
      throw new Error("Admin or customer not defined");

    // Create a social link to delete
    const { data: newLink } = await admin.client
      .schema("app_account")
      .from("social_link")
      .insert({
        user_id: customer1.data.id,
        platform: platformId,
        link: "https://example.com/admin-to-delete"
      })
      .select()
      .single();

    if (!newLink?.id) throw new Error("Failed to create social link");

    const { error } = await admin.client
      .schema("app_account")
      .from("social_link")
      .delete()
      .eq("id", newLink.id);

    expect(error).toBeNull();

    // Verify it's deleted
    const { data: deletedLink } = await admin.client
      .schema("app_account")
      .from("social_link")
      .select("*")
      .eq("id", newLink.id);

    expect(deletedLink).toHaveLength(0);

    // No need to track this ID since it's deleted
  });
});

describe("Provider Role Tests", () => {
  test("provider can manage their own social links", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider not defined");

    // Create
    const { data: created, error: createError } = await provider.client
      .schema("app_account")
      .from("social_link")
      .insert({
        user_id: provider.data.id,
        platform: platformId,
        link: "https://example.com/provider"
      })
      .select()
      .single();

    expect(createError).toBeNull();
    expect(created?.user_id).toBe(provider.data.id);
    expect(created?.link).toBe("https://example.com/provider");
    expect(created?.platform).toBe(platformId);
    expect(created?.id).toBeDefined();

    if (!created?.id) throw new Error("Failed to create provider link");

    // Update
    const { data: updated, error: updateError } = await provider.client
      .schema("app_account")
      .from("social_link")
      .update({
        link: "https://example.com/provider-updated"
      })
      .eq("id", created.id)
      .select()
      .single();

    expect(updateError).toBeNull();
    expect(updated?.link).toBe("https://example.com/provider-updated");
    expect(updated?.user_id).toBe(provider.data.id);
    expect(updated?.platform).toBe(platformId);
    expect(updated?.id).toBe(created.id);

    // Delete
    const { error: deleteError } = await provider.client
      .schema("app_account")
      .from("social_link")
      .delete()
      .eq("id", created.id);

    expect(deleteError).toBeNull();

    // Verify it's deleted
    const { data: deletedLink } = await provider.client
      .schema("app_account")
      .from("social_link")
      .select("*")
      .eq("id", created.id);

    expect(deletedLink).toHaveLength(0);

    // No need to track this ID since it's deleted
  });
});

describe("Platform Reference Tests", () => {
  test("social link requires valid platform reference", async () => {
    if (!customer1.client || !customer1.data)
      throw new Error("Customer not defined");

    const { error } = await customer1.client
      .schema("app_account")
      .from("social_link")
      .insert({
        user_id: customer1.data.id,
        platform: 99999, // Non-existent platform
        link: "https://example.com/invalid-platform"
      });

    expect(error).not.toBeNull();
    expect(error?.code).toBe("23503"); // foreign_key_violation
  });

  test("social link is deleted when platform is deleted", async () => {
    if (!customer1.client || !customer1.data)
      throw new Error("Customer not defined");

    // Create a temporary platform using service client
    const { data: tempPlatform } = await serviceClient
      .schema("app_catalog")
      .from("platform")
      .insert({
        name: "Temporary Platform"
      })
      .select()
      .single();

    if (!tempPlatform?.id) throw new Error("Failed to create temp platform");

    // Create a social link with this platform
    const { data: socialLink } = await customer1.client
      .schema("app_account")
      .from("social_link")
      .insert({
        user_id: customer1.data.id,
        platform: tempPlatform.id,
        link: "https://example.com/temp-platform"
      })
      .select()
      .single();

    if (!socialLink?.id) throw new Error("Failed to create social link");

    // Verify the social link was created with correct values
    expect(socialLink.user_id).toBe(customer1.data.id);
    expect(socialLink.platform).toBe(tempPlatform.id);
    expect(socialLink.link).toBe("https://example.com/temp-platform");

    // Delete the platform using service client
    await serviceClient
      .schema("app_catalog")
      .from("platform")
      .delete()
      .eq("id", tempPlatform.id);

    // Verify social link is also deleted due to cascade
    const { data: deletedLink } = await serviceClient
      .schema("app_account")
      .from("social_link")
      .select("*")
      .eq("id", socialLink.id);

    expect(deletedLink).toHaveLength(0);

    // No need to track IDs since they're both deleted
  });
});
