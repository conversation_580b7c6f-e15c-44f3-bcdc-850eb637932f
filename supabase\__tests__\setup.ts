import { beforeAll, afterAll } from "vitest";
import { dbClient } from "./utils/client";
import {
  getRateLimit,
  resetRateLimits,
  setRateLimit
} from "./utils/rate-limit";

let originalMaxRequestsPerMinute: number;

beforeAll(async () => {
  await dbClient.connect();

  originalMaxRequestsPerMinute = await getRateLimit();

  await setRateLimit(100000000);

  await resetRateLimits();
});

afterAll(async () => {
  await resetRateLimits();

  await setRateLimit(originalMaxRequestsPerMinute);

  await dbClient.end();
});
