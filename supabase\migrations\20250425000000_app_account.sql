-- section <PERSON><PERSON>EM<PERSON>
DROP SCHEMA IF EXISTS app_account CASCADE
;

CREATE SCHEMA IF NOT EXISTS app_account
;

GRANT USAGE ON SCHEMA app_account TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_account TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_account TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_account TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_account
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_account
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_account
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section ENUMS
-- anchor KYC_STATUS
CREATE TYPE app_account.KYC_STATUS AS ENUM(
  'draft',
  'pending',
  'approved',
  'rejected'
)
;

-- !section
-- section TABLES
-- anchor profile
CREATE TABLE app_account.profile (
  user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
  username TEXT UNIQUE NOT NULL CHECK (
    LENGTH(username) >= 3
    AND LENGTH(username) <= 20
    AND username ~ '^[a-z0-9]+(?:-[a-z0-9]+)*$'
  ),
  nickname TEXT CHECK (
    LENGTH(nickname) >= 3
    AND LENGTH(nickname) <= 30
  ),
  bio TEXT CHECK (LENGTH(bio) <= 500),
  gender app_core.GENDER,
  join_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  birth_date DATE,
  CHECK (
    birth_date <= NOW() - INTERVAL '18 years'
  )
)
;

-- anchor locale
CREATE TABLE app_account.locale (
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  locale app_core.locale,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (user_id, locale)
)
;

-- anchor kyc
CREATE TABLE app_account.kyc (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  user_id UUID UNIQUE REFERENCES auth.users (id) ON DELETE CASCADE,
  status app_account.KYC_STATUS NOT NULL DEFAULT 'pending',
  full_name TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor iban
CREATE TABLE app_account.iban (
  user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
  iban TEXT NOT NULL CHECK (
    iban ~ '^[A-Z]{2}[0-9]{2}[A-Z0-9]{11,30}$'
  ),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor privacy
CREATE TABLE app_account.privacy (
  user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
  show_activity BOOLEAN NOT NULL DEFAULT TRUE,
  show_in_leaderboard BOOLEAN NOT NULL DEFAULT TRUE
)
;

-- anchor user_block
CREATE TABLE app_account.user_block (
  blocker_id UUID NOT NULL DEFAULT auth.uid () REFERENCES auth.users (id) ON DELETE CASCADE,
  blocked_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  PRIMARY KEY (blocker_id, blocked_id),
  CHECK (blocker_id <> blocked_id)
)
;

-- anchor banned_user
CREATE TABLE app_account.banned_user (
  user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
  previous_roles TEXT[] NOT NULL DEFAULT '{}',
  banned_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  banned_by UUID REFERENCES auth.users (id),
  reason TEXT,
  CHECK (
    ARRAY_LENGTH(previous_roles, 1) IS NULL
    OR ARRAY_LENGTH(previous_roles, 1) > 0
  )
)
;

-- anchor visit
CREATE TABLE app_account.visit (
  visitor_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  visited_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  visited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (visitor_id, visited_id),
  CHECK (visitor_id <> visited_id)
)
;

-- anchor favorite_activity
CREATE TABLE app_account.favorite_activity (
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  activity_id UUID NOT NULL REFERENCES app_catalog.activity (id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (user_id, activity_id)
)
;

-- anchor social_link
CREATE TABLE app_account.social_link (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  platform INT NOT NULL REFERENCES app_catalog.platform (id) ON DELETE CASCADE,
  link TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor notification
CREATE TABLE app_account.notification (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  type TEXT NOT NULL,
  recipient_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  read_at TIMESTAMP WITH TIME ZONE,
  title_key TEXT NOT NULL,
  message_key TEXT NOT NULL,
  data JSONB NOT NULL,
  action_url TEXT
)
;

-- !section
-- section FUNCTIONS
-- anchor seize_user_roles_on_ban
CREATE OR REPLACE FUNCTION app_account.seize_user_roles_on_ban () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_role_names TEXT[];
BEGIN
  -- Get all current role names for the user
  SELECT ARRAY_AGG(r.name) INTO v_role_names
  FROM app_access.user_role ur
  JOIN app_access.role r ON ur.role_id = r.id
  WHERE ur.user_id = NEW.user_id;

  -- Store the roles in the banned_user record
  UPDATE app_account.banned_user
  SET previous_roles = COALESCE(v_role_names, '{}')
  WHERE user_id = NEW.user_id;

  -- Remove all roles from the user
  DELETE FROM app_access.user_role
  WHERE user_id = NEW.user_id;

  RETURN NEW;
END;
$$
;

-- anchor restore_user_roles_on_unban
CREATE OR REPLACE FUNCTION app_account.restore_user_roles_on_unban () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_role_name TEXT;
BEGIN
  -- Restore all previous roles
  FOREACH v_role_name IN ARRAY OLD.previous_roles
  LOOP
    PERFORM app_access.assign_role_to_user(OLD.user_id, v_role_name);
  END LOOP;

  RETURN OLD;
END;
$$
;

-- anchor record_profile_visit
CREATE OR REPLACE FUNCTION app_account.record_profile_visit (p_visited_id UUID) RETURNS VOID LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_visitor_id UUID := auth.uid();
BEGIN
  -- Check if user has capability to view profiles
  IF NOT app_access.has_capability('account.profile.visit') THEN
    RAISE EXCEPTION 'Insufficient permissions to visit profiles';
  END IF;

  -- Check if visitor and visited are different users
  IF v_visitor_id = p_visited_id THEN
    RETURN; -- Don't record self-visits
  END IF;

  -- Check if both users exist
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = v_visitor_id) THEN
    RAISE EXCEPTION 'Visitor user does not exist';
  END IF;

  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_visited_id) THEN
    RAISE EXCEPTION 'Visited user does not exist';
  END IF;

  -- Insert or update the visit record (upsert)
  INSERT INTO app_account.visit (visitor_id, visited_id, visited_at)
  VALUES (v_visitor_id, p_visited_id, NOW())
  ON CONFLICT (visitor_id, visited_id)
  DO UPDATE SET visited_at = NOW();

  -- Clean up old visit records (older than 1 day) for this visitor
  DELETE FROM app_account.visit
  WHERE visitor_id = v_visitor_id
    AND visited_at < NOW() - INTERVAL '1 day';
END;
$$
;

-- anchor read_notification
CREATE OR REPLACE FUNCTION app_account.read_notification (p_notification_id UUID) RETURNS VOID LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_current_user_id UUID := auth.uid();
BEGIN
  UPDATE app_account.notification
  SET read_at = NOW()
  WHERE id = p_notification_id
    AND recipient_id = v_current_user_id;
END;
$$
;

-- anchor create_notification
CREATE OR REPLACE FUNCTION app_account.create_notification (
  p_type TEXT,
  p_recipient_id UUID,
  p_title_key TEXT,
  p_message_key TEXT,
  p_data JSONB,
  p_action_url TEXT DEFAULT NULL
) RETURNS app_account.notification LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_new_notification app_account.notification;
BEGIN
  INSERT INTO app_account.notification (
    type,
    recipient_id,
    title_key,
    message_key,
    data,
    action_url
  ) VALUES (
    p_type,
    p_recipient_id,
    p_title_key,
    p_message_key,
    p_data,
    p_action_url
  ) RETURNING * INTO v_new_notification;

  -- Broadcast to notification:recipient_id
  PERFORM realtime.send(
    row_to_json(v_new_notification)::jsonb,
    'INSERT',
    'notification:' || p_recipient_id::text,
    true
  );

  RETURN v_new_notification;
END;
$$
;

-- !section
-- section TRIGGERS
-- anchor kyc
CREATE TRIGGER kyc_set_updated_at BEFORE
UPDATE ON app_account.kyc FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- anchor iban
CREATE TRIGGER iban_set_updated_at BEFORE
UPDATE ON app_account.iban FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- anchor banned_user
CREATE TRIGGER banned_user_seize_roles
AFTER INSERT ON app_account.banned_user FOR EACH ROW
EXECUTE FUNCTION app_account.seize_user_roles_on_ban ()
;

CREATE TRIGGER banned_user_restore_roles
AFTER DELETE ON app_account.banned_user FOR EACH ROW
EXECUTE FUNCTION app_account.restore_user_roles_on_unban ()
;

-- !section
-- section RLS POLICIES
-- anchor profile
ALTER TABLE app_account.profile ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "profile_select_public" ON app_account.profile FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "profile_select_own" ON app_account.profile FOR
SELECT
  USING (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.profile.view')
  )
;

CREATE POLICY "profile_insert_own" ON app_account.profile FOR INSERT
WITH
  CHECK (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.profile.edit')
  )
;

CREATE POLICY "profile_update_own" ON app_account.profile
FOR UPDATE
  USING (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.profile.edit')
  )
;

CREATE POLICY "profile_manage_admin" ON app_account.profile FOR ALL USING (
  app_access.has_capability ('account.profile.all.edit')
)
WITH
  CHECK (
    app_access.has_capability ('account.profile.all.edit')
  )
;

-- anchor locale
ALTER TABLE app_account.locale ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "locale_select_own" ON app_account.locale FOR
SELECT
  USING (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.locale.view')
  )
;

CREATE POLICY "locale_manage_own" ON app_account.locale FOR ALL USING (
  user_id = auth.uid ()
  AND app_access.has_capability ('account.locale.edit')
)
WITH
  CHECK (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.locale.edit')
  )
;

CREATE POLICY "locale_manage_admin" ON app_account.locale FOR ALL USING (
  app_access.has_capability ('account.locale.all.edit')
)
WITH
  CHECK (
    app_access.has_capability ('account.locale.all.edit')
  )
;

-- anchor kyc
ALTER TABLE app_account.kyc ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "kyc_select_own" ON app_account.kyc FOR
SELECT
  USING (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.kyc.view')
  )
;

CREATE POLICY "kyc_manage_own_draft_pending" ON app_account.kyc FOR ALL USING (
  user_id = auth.uid ()
  AND app_access.has_capability ('account.kyc.edit')
  AND status IN ('draft', 'pending')
)
WITH
  CHECK (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.kyc.edit')
    AND status IN ('draft', 'pending')
  )
;

CREATE POLICY "kyc_manage_admin" ON app_account.kyc FOR ALL USING (
  app_access.has_capability ('account.kyc.all.edit')
)
WITH
  CHECK (
    app_access.has_capability ('account.kyc.all.edit')
  )
;

-- anchor iban
ALTER TABLE app_account.iban ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "iban_select_own" ON app_account.iban FOR
SELECT
  USING (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.iban.view')
  )
;

CREATE POLICY "iban_manage_own" ON app_account.iban FOR ALL USING (
  user_id = auth.uid ()
  AND app_access.has_capability ('account.iban.edit')
)
WITH
  CHECK (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.iban.edit')
  )
;

CREATE POLICY "iban_manage_admin" ON app_account.iban FOR ALL USING (
  app_access.has_capability ('account.iban.all.edit')
)
WITH
  CHECK (
    app_access.has_capability ('account.iban.all.edit')
  )
;

-- anchor privacy
ALTER TABLE app_account.privacy ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "privacy_select_own" ON app_account.privacy FOR
SELECT
  USING (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.privacy.view')
  )
;

CREATE POLICY "privacy_manage_own" ON app_account.privacy FOR ALL USING (
  user_id = auth.uid ()
  AND app_access.has_capability ('account.privacy.edit')
)
WITH
  CHECK (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.privacy.edit')
  )
;

CREATE POLICY "privacy_manage_admin" ON app_account.privacy FOR ALL USING (
  app_access.has_capability ('account.privacy.all.edit')
)
WITH
  CHECK (
    app_access.has_capability ('account.privacy.all.edit')
  )
;

-- anchor user_block
ALTER TABLE app_account.user_block ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "user_block_select_own" ON app_account.user_block FOR
SELECT
  USING (
    blocker_id = auth.uid ()
    AND app_access.has_capability ('account.user_block.view')
  )
;

CREATE POLICY "user_block_manage_own" ON app_account.user_block FOR ALL USING (
  blocker_id = auth.uid ()
  AND app_access.has_capability ('account.user_block.edit')
)
WITH
  CHECK (
    blocker_id = auth.uid ()
    AND app_access.has_capability ('account.user_block.edit')
  )
;

CREATE POLICY "user_block_manage_admin" ON app_account.user_block FOR ALL USING (
  app_access.has_capability ('account.user_block.all.edit')
)
WITH
  CHECK (
    app_access.has_capability ('account.user_block.all.edit')
  )
;

-- anchor banned_user
ALTER TABLE app_account.banned_user ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "banned_user_select_admin" ON app_account.banned_user FOR
SELECT
  USING (
    app_access.has_capability ('account.banned_user.view')
  )
;

CREATE POLICY "banned_user_manage_admin" ON app_account.banned_user FOR ALL USING (
  app_access.has_capability ('account.banned_user.manage')
)
WITH
  CHECK (
    app_access.has_capability ('account.banned_user.manage')
  )
;

-- anchor visit
ALTER TABLE app_account.visit ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "visit_select_own_visits" ON app_account.visit FOR
SELECT
  USING (
    visitor_id = auth.uid ()
    AND app_access.has_capability ('account.visit.view')
  )
;

CREATE POLICY "visit_select_own_visitors" ON app_account.visit FOR
SELECT
  USING (
    visited_id = auth.uid ()
    AND app_access.has_capability ('account.visit.view')
  )
;

CREATE POLICY "visit_select_admin" ON app_account.visit FOR
SELECT
  USING (
    app_access.has_capability ('account.visit.all.view')
  )
;

CREATE POLICY "visit_manage_admin" ON app_account.visit FOR ALL USING (
  app_access.has_capability ('account.visit.all.edit')
)
WITH
  CHECK (
    app_access.has_capability ('account.visit.all.edit')
  )
;

-- anchor favorite_activity
ALTER TABLE app_account.favorite_activity ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "favorite_activity_select_own" ON app_account.favorite_activity FOR
SELECT
  USING (
    user_id = auth.uid ()
    AND app_access.has_capability (
      'account.favorite_activity.view'
    )
  )
;

CREATE POLICY "favorite_activity_select_all" ON app_account.favorite_activity FOR
SELECT
  USING (
    app_access.has_capability (
      'account.favorite_activity.all.view'
    )
  )
;

CREATE POLICY "favorite_activity_manage_own" ON app_account.favorite_activity FOR ALL USING (
  user_id = auth.uid ()
  AND app_access.has_capability (
    'account.favorite_activity.edit'
  )
)
WITH
  CHECK (
    user_id = auth.uid ()
    AND app_access.has_capability (
      'account.favorite_activity.edit'
    )
  )
;

CREATE POLICY "favorite_activity_manage_admin" ON app_account.favorite_activity FOR ALL USING (
  app_access.has_capability (
    'account.favorite_activity.all.edit'
  )
)
WITH
  CHECK (
    app_access.has_capability (
      'account.favorite_activity.all.edit'
    )
  )
;

-- anchor social_link
ALTER TABLE app_account.social_link ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "social_link_select_all" ON app_account.social_link FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "social_link_insert_own" ON app_account.social_link FOR INSERT
WITH
  CHECK (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.social_link.edit')
  )
;

CREATE POLICY "social_link_update_own" ON app_account.social_link
FOR UPDATE
  USING (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.social_link.edit')
  )
WITH
  CHECK (
    user_id = auth.uid ()
    AND app_access.has_capability ('account.social_link.edit')
  )
;

CREATE POLICY "social_link_delete_own" ON app_account.social_link FOR DELETE USING (
  user_id = auth.uid ()
  AND app_access.has_capability ('account.social_link.edit')
)
;

CREATE POLICY "social_link_manage_admin" ON app_account.social_link FOR ALL USING (
  app_access.has_capability (
    'account.social_link.all.edit'
  )
)
WITH
  CHECK (
    app_access.has_capability (
      'account.social_link.all.edit'
    )
  )
;

-- anchor notification
ALTER TABLE app_account.notification ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "notification_select_own" ON app_account.notification FOR
SELECT
  USING (
    recipient_id = auth.uid ()
    AND app_access.has_capability ('account.notification.view')
  )
;

CREATE POLICY "notification_manage_admin" ON app_account.notification FOR ALL USING (
  app_access.has_capability (
    'account.notification.all.edit'
  )
)
WITH
  CHECK (
    app_access.has_capability (
      'account.notification.all.edit'
    )
  )
;

-- !section
-- section CAPABILITIES
-- anchor admin
SELECT
  app_access.define_role_capability (
    'admin',
    ARRAY[
      'account.profile.all.view',
      'account.profile.all.edit',
      'account.iban.all.view',
      'account.iban.all.edit',
      'account.kyc.all.view',
      'account.kyc.all.edit',
      'account.locale.all.view',
      'account.locale.all.edit',
      'account.privacy.all.view',
      'account.privacy.all.edit',
      'account.user_block.all.view',
      'account.user_block.all.edit',
      'account.banned_user.view',
      'account.banned_user.manage',
      'account.visit.all.view',
      'account.visit.all.edit',
      'account.profile.visit',
      'account.favorite_activity.all.view',
      'account.favorite_activity.all.edit',
      'account.social_link.all.edit',
      'account.notification.all.view',
      'account.notification.all.edit'
    ]
  )
;

-- anchor customer
SELECT
  app_access.define_role_capability (
    'customer',
    ARRAY[
      'account.profile.view',
      'account.profile.edit',
      'account.locale.view',
      'account.locale.edit',
      'account.kyc.view',
      'account.kyc.edit',
      'account.iban.view',
      'account.iban.edit',
      'account.privacy.view',
      'account.privacy.edit',
      'account.user_block.view',
      'account.user_block.edit',
      'account.visit.view',
      'account.profile.visit',
      'account.favorite_activity.view',
      'account.favorite_activity.edit',
      'account.social_link.edit',
      'account.notification.view'
    ]
  )
;

-- anchor provider
SELECT
  app_access.define_role_capability (
    'provider',
    ARRAY[
      'account.profile.view',
      'account.profile.edit',
      'account.locale.view',
      'account.locale.edit',
      'account.kyc.view',
      'account.kyc.edit',
      'account.iban.view',
      'account.iban.edit',
      'account.privacy.view',
      'account.privacy.edit',
      'account.user_block.view',
      'account.user_block.edit',
      'account.visit.view',
      'account.profile.visit',
      'account.favorite_activity.all.view',
      'account.favorite_activity.edit',
      'account.social_link.edit',
      'account.notification.view'
    ]
  )
;

-- !section